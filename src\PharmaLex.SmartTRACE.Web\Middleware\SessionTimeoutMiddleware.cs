using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Http;
using System;
using System.Threading.Tasks;

namespace PharmaLex.SmartTRACE.Web.Middleware
{
    public class SessionTimeoutMiddleware : IMiddleware
    {
        private readonly int _timeoutMinutes;

        public SessionTimeoutMiddleware(int timeoutMinutes = 20)
        {
            _timeoutMinutes = timeoutMinutes;
        }

        public async Task InvokeAsync(HttpContext context, RequestDelegate next)
        {
            // Try to get authentication result directly (works better with Azure AD B2C)
            var authResult = await context.AuthenticateAsync();

            if (authResult.Succeeded && authResult.Principal != null)
            {
                var now = DateTimeOffset.UtcNow;
                var properties = authResult.Properties;

                // Check if expiration needs to be set or updated
                bool needsUpdate = false;

                if (!properties.ExpiresUtc.HasValue)
                {
                    // No expiration set, set it to 20 minutes from now
                    properties.ExpiresUtc = now.AddMinutes(_timeoutMinutes);
                    properties.IssuedUtc = now;
                    needsUpdate = true;
                }
                else
                {
                    var issuedUtc = properties.IssuedUtc ?? now.AddMinutes(-_timeoutMinutes);
                    var timeElapsed = now - issuedUtc;

                    // If more than half the timeout has passed, refresh the cookie (sliding expiration)
                    if (timeElapsed.TotalMinutes > (_timeoutMinutes / 2))
                    {
                        properties.ExpiresUtc = now.AddMinutes(_timeoutMinutes);
                        properties.IssuedUtc = now;
                        needsUpdate = true;
                    }
                }

                // Re-sign the user in with updated properties if needed
                if (needsUpdate)
                {
                    await context.SignInAsync(authResult.Principal, properties);
                }
            }

            await next(context);
        }
    }
}
